/** 图片工具函数 */

/** 图片文件转base64编码 */
export const imageToBase64: (file: File) => Promise<string> = (file: File) => {
  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64Str: string | undefined = reader.result?.toString();
      if (base64Str) {
        resolve(base64Str);
      } else {
        reject(new Error("图片转码失败"));
      }
    };

    reader.onerror = (err) => {
      reject(err);
    };
    reader.readAsDataURL(file);
  });
};

//将base64转换为文件
export const dataURLtoFile = (dataurl, filename) => {
  var arr = dataurl.split(","),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
};
