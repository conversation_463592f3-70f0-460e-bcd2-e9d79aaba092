# 后端请求地址
VITE_API_BASE=http://**************:6001/api
# 服务前缀
VITE_API_BASE_PUB=/langwell-pub-server
VITE_API_BASE_SYS=/langwell-sys-server
VITE_API_BASE_NOTE=/langwell-notes-server
VITE_API_BASE_AI=/langwell-ai-server
VITE_API_BASE_INS=/langwell-ins-server
# VITE_API_BASE_INS=/langwell-ins-flux-server
VITE_API_BASE_DOC=/langwell-doc-server
# API凭据key
VITE_API_HEADER_KEY=SA-TOKEN
# 授权方式
VITE_AUTHORIZE_MODE=account
# AI后端请求地址
VITE_AI_API_BASE=http://192.168.113.90:88/v1
VITE_AI_CHAT_SECRET=app-hLZ652C967Dxkcz5YgRkNObh
VITE_AI_CHAT_AGENT_ID=0f31b4cb-5752-409d-b47d-38a9c1e162d6
# VITE_AI_WRITER_SECRET=app-3W8o0WytWFbmOvtjvxMizDOO
VITE_AI_WRITER_SECRET=7431d2e0-e6be-437e-b3af-4789dd6893f2
# VITE_AI_REPLY_SECRET=app-1xqMPA2tUIGDFXz4aIeF1lAv
VITE_AI_REPLY_SECRET=42b6cf25-971c-4bcc-a86e-1004a7fb5702

# 租户用户信息
VITE_USERINFO_BASS=http://**************:18760/api
# 官网页面地址
VITE_OFFICIAL_URL=http://**************:6008
# VITE_OFFICIAL_URL=http://***************:3000

# 授权相关配置
VITE_CORP_ID=wwd7e14e07bf973c80
VITE_AGENT_ID=1000085
VITE_AUTHORIZE_DOMAIN=https://scrm.sino-bridge.com:8098

# 文件前缀
VITE_FILE_PREFIX=https://copilot.sino-bridge.com:90

# 是否开启实验功能
VITE_ENABLE_LAB_FEATURES=false

# 工具地址
VITE_TOOLBOX_URL=http://**************:6001/toolbox

# mqtt配置
VITE_MQTT_PROTOCOL=ws
VITE_MQTT_HOST=**************
VITE_MQTT_PORT=6008
VITE_MQTT_USERNAME=your-mqtt-username
VITE_MQTT_PASSWORD=your-mqtt-password
VITE_MQTT_PATH=/mqttSocket/mqtt
