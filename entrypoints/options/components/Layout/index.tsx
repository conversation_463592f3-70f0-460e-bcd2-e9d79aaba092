/** 个人设置 */
import React, { useEffect, useState } from "react";
import "./index.less";
import CueWord from "./components/cueWord/index";
import Note from "./components/note/index";
import Message from "./components/message/index";
import SessionRecord from "./components/sessionRecord/index";
import MySettings from "./components/mySettings/index";
import ShortcutKey from "./components/shortcutKey";
import InsManage from "./components/insManage/index";
import TeamManage from "./components/teamManage/index";
import SmartMenu from "./components/smartMenu/index";
import { Flex, Layout, Menu } from "antd";
type StateProps = { userId: string; menu: string };
const { Content, Sider } = Layout;
// Tab页签数据配置
const SetupComponent: React.FC = () => {
  // 设置菜单
  const menuList = [
    { id: "5", label: "我的设置", key: "5" },
    // { id: "1", label: "提示词管理", key: "1" },
    { id: "7", label: "指令管理", key: "7" },
    { id: "2", label: "便签清单", key: "2" },
    { id: "3", label: "消息管理", key: "3" },
    { id: "4", label: "聊天历史", key: "4" },
    { id: "6", label: "快捷键设置", key: "6" },
    // { id: "8", label: "团队管理", key: "8" },
    { id: "9", label: "智能菜单显影", key: "9" },
  ];
  const [state, setState] = useState<StateProps>();
  // 菜单点击
  const menuClick = ({ item, key, keyPath, domEvent }) => {
    setState({ ...state, menu: key });
  };
  useEffect(() => {
    const close = (message, sender, sendResponse) => {
      if (message.type === "updateOptions") {
        window.close();
      }
    };
    browser.runtime.onMessage.addListener(close);
    return () => {
      browser.runtime.onMessage.removeListener(close);
    };
  }, []);
  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const messa = queryParams.get("message");
    if (messa) {
      setState({ userId: "", menu: messa });
    } else {
      setState({ userId: "", menu: "5" });
    }
  }, []);

  const topStyle: React.CSSProperties = {
    position: "absolute",
    left: "0",
    top: "0",
    width: "200px",
    height: "200px",
    background: "linear-gradient(180deg, rgba(189, 225, 255, 0.4) 0%, rgba(224, 242, 255, 0) 100%)",
    filter: "blur(54.4px)",
    zIndex: 1,
    pointerEvents: "none",
  };
  return (
    <Flex className="setup-content">
      <Flex className="setup-con-ls">
        <Layout hasSider>
          <Sider className="setup-side-left">
            <Flex className="setup-side-title">
              <img src="/images/logo.png" alt="logo"></img>
              <Flex className="setup-side-text">设置</Flex>
            </Flex>
            <Menu
              theme="light"
              mode="inline"
              style={{ borderRight: 0 }}
              selectedKeys={state ? [state.menu] : ["5"]}
              items={menuList}
              onClick={menuClick}
            />
          </Sider>
          <Content style={{ position: "relative" }}>
            <div style={topStyle} />
            {state && state.menu == "5" && <MySettings />}
            {state && state.menu == "7" && <InsManage />}
            {state && state.menu == "1" && <CueWord />}
            {state && state.menu == "2" && <Note />}
            {state && state.menu == "4" && <SessionRecord />}
            {state && state.menu == "3" && <Message />}
            {state && state.menu == "6" && <ShortcutKey />}
            {state && state.menu == "8" && <TeamManage />}
            {state && state.menu == "9" && <SmartMenu />}
          </Content>
        </Layout>
      </Flex>
      {/* <div className="setup-side-left">
        <div className="setup-side-title">
          <img src="/images/logo.png" alt="logo"></img>
          <div className="setup-side-text">设置</div>
        </div>
        {menuList.map((item) => {
          return (
            <div
              className={state.menu == item.id ? "setup-side-item-selected" : "setup-side-item"}
              key={item.id}
              onClick={() => setState({ ...state, menu: item.id })}
            >
              {item.label}
            </div>
          );
        })}
      </div>
      <div style={{ flex: 1 }}>
        {state.menu == "1" && <CueWord />}
        {state.menu == "2" && <Note />}
        {state.menu == "3" && <Message />}
      </div> */}
    </Flex>
  );
};

export default SetupComponent;
