@import "@/assets/styles/variables";
.side-panel-title {
  z-index: 100;
  padding: 16px 0;
  width: 100%;
  position: fixed;
  top: 0;
  .common {
    line-height: var(--ant-line-height-heading-2);
    font-family: @side-panel-font-family-bold;
    font-size: var(--ant-font-size-heading-4);
  }
  .title-text {
    color: transparent;
    background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
    -webkit-background-clip: text;
    user-select: none;
  }
  .back-arrow {
    width: 100%;
  }
  .title-text-detail {
    width: calc(100% - 60px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: var(--ant-color-text-base);
    display: inline-block;
  }
  .back-width {
    font-size: var(--ant-font-size-heading-4);
    color: var(--ant-color-text);
    cursor: pointer;
    margin-right: 6px;
  }
}
