import { Flex, Layout } from "antd";
import "./index.less";
import { LeftOutlined } from "@ant-design/icons";
interface TopTitleProps {
  title: string;
  titleDetail?: boolean;
  handleBackPar?: () => void;
}
const topTitle: React.FC<TopTitleProps> = ({ title, titleDetail, handleBackPar }) => {
  return (
    <>
      <Flex className="side-panel-title" align="center">
        {titleDetail ? (
          <Flex className="back-arrow" style={{ cursor: "pointer" }} onClick={handleBackPar}>
            <LeftOutlined className="back-width " />
            <Flex className="title-text-detail common">{title}</Flex>
          </Flex>
        ) : (
          <Flex className="title-text common">{title}</Flex>
        )}
      </Flex>
    </>
  );
};

export default topTitle;
