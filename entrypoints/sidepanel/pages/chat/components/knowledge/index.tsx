import React, { useRef, useState } from "react";
import type { CheckboxProps } from "antd";
import { Checkbox, Flex, Input, Modal, Spin } from "antd";
// import CreateKnowledge from "./components/createKnowledge";
import "./index.less";
import { getContainer } from "@/utils";
import { DatabaseTwoTone, SearchOutlined } from "@ant-design/icons";
import classNames from "classnames";

interface CardData {
  id: number | string;
  libName: string;
  libDesc: string;
  checked: boolean;
}

interface Props {
  cardData: CardData[];
  knowledModel: boolean;
  closeKnowledModel: (boolean, string) => void;
  keywordSearch: (keyword) => void;
  onCheckboxChange: (string, boolean) => void;
  closeKnowledModelFalg: (boolean) => void;
}

const Knowledge: React.FC<Props> = ({
  cardData,
  knowledModel,
  closeKnowledModel,
  keywordSearch,
  onCheckboxChange,
  closeKnowledModelFalg,
}) => {
  const [selectKnowdgeId, setSelectKnowdgeId] = useState<any>(
    cardData.filter((item) => item.checked).map((item) => item.id),
  );
  const [loading, setLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handlerSubmit = () => {
    closeKnowledModel(false, selectKnowdgeId);
  };
  const closeMoal = () => {
    closeKnowledModelFalg(false);
  };
  const onChange: CheckboxProps["onChange"] = (e, item) => {
    if (e.target.checked) {
      setSelectKnowdgeId([...selectKnowdgeId, item.id]);
    } else {
      let selectId = [];
      for (let i = 0; i < selectKnowdgeId.length; i++) {
        if (selectKnowdgeId[i] != item.id) {
          selectId.push(selectKnowdgeId[i]);
        }
      }
      setSelectKnowdgeId(selectId);
    }

    onCheckboxChange(item.id, e.target.checked);
  };
  const inputChange = (e) => {
    keywordSearch(e.target.value.trim());
  };

  return (
    <div className="char-knowledge-content">
      {/* 知识库详情 */}
      <div className="content-box">
        {/* 文件知识库 */}
        <Modal
          title="选择知识库"
          centered
          width={300}
          getContainer={getContainer}
          open={knowledModel}
          onOk={handlerSubmit}
          onCancel={closeMoal}
          okText="确认"
          cancelText="取消"
        >
          <>
            <Flex className="chat-search-input">
              <Input
                ref={inputRef}
                onChange={inputChange}
                allowClear
                prefix={<SearchOutlined />}
                placeholder="搜索知识库"
                style={{ width: "100%" }}
              />
            </Flex>
            <Spin spinning={loading} size="default">
              <Flex className="modal-know-wcl" vertical>
                {cardData &&
                  cardData.map((item, index) => {
                    return (
                      <Flex key={index} className={classNames("cardBox-wcl", item.checked ? "acitve" : "")}>
                        <Checkbox checked={item.checked} onChange={(e) => onChange(e, item)}>
                          <Flex className="top" justify="center" align="center">
                            <Flex className="icon-card">
                              <DatabaseTwoTone />
                            </Flex>
                            <Flex className="left-gas" vertical>
                              <Flex>
                                <Flex className="first-title">{item.libName}</Flex>
                              </Flex>
                              <Flex className="two-title">{item.libDesc}</Flex>
                            </Flex>
                          </Flex>
                        </Checkbox>
                      </Flex>
                    );
                  })}
              </Flex>
            </Spin>
          </>
        </Modal>
      </div>
    </div>
  );
};

export default React.memo(Knowledge);
