import React, { useCallback, useEffect, useRef, useState } from "react";
import type { MenuProps, UploadProps } from "antd";
import {
  Button,
  Col,
  Dropdown,
  Flex,
  Form,
  Image,
  Input,
  message,
  Modal,
  Popconfirm,
  Progress,
  Row,
  Select,
  Spin,
  Tooltip,
  Typography,
  Upload,
} from "antd";
// import CreateKnowledge from "./components/createKnowledge";
import { getUserInfo, UserInfo } from "@/utils/auth.ts";
import "./index.less";
import { getContainer } from "@/utils";
import { knowdgeSVGIcon } from "@/config/menu/knowdge";
// import HistoryDrawer from "./components/HistoryDrawer/index";
import classnames from "classnames";
import { formatDate } from "@/utils/dateFormat.ts";
import {
  DatabaseOutlined,
  DeleteOutlined,
  DesktopOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  FileAddOutlined,
  FileOutlined,
  FolderOutlined,
  InboxOutlined,
  LockOutlined,
  MoreOutlined,
  PlusOutlined,
  SearchOutlined,
  UploadOutlined,
} from "@ant-design/icons";
// import InfiniteScroll from "react-infinite-scroll-component";
import TopTitle from "../../components/TopTitle";
import EmptyData from "@/components/EmptyData";
import { base64ToBlob } from "@/utils/screenshot";

const { TextArea } = Input;

const Knowledge: React.FC = (props) => {
  const fetchRequest = useFetchRequest();
  const [form] = Form.useForm();
  const searchParamsInit = {
    pageNum: 1,
    pageSize: 300,
    entity: {
      libName: "",
      libType: "TEXT_LIBRARY",
    },
  };

  const [currentData, setCurrentData] = useState<any>({}); // 用于存储当前编辑的数据
  const [cardData, setCardData] = useState<any>([]);
  const [addOredit, setAddOredit] = useState<string>("add");
  const [modal2Open, setModal2Open] = useState(false);
  const [modalUploadOpen, setModalUploadOpen] = useState(false);
  const [modalFileOpen, setModalFileOpen] = useState(false);
  const [toDetaile, setToDetaile] = useState(false);
  const [itemDetail, setItemDetail] = useState<any>([]);
  const [knowHome, setknowHome] = useState<string>("首页");
  const [interToDetaile, setInterToDetaile] = useState(false);
  const [interFlag, setInterFlag] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const [searchParams, setSearchParams] = useState(searchParamsInit);
  const [currentAi, setCurrentAi] = useState<string>();
  const [userInfo, setUserInfo] = useState<UserInfo>({});
  const [openHistoryDrawer, setOpenHistoryDrawer] = useState<boolean>(false);
  const [itemSelectIndex, setItemSelectIndex] = useState<number>(null);
  const [currentKnow, setCurrentKnow] = useState<{ id: string; ossId: string; baseFileId: number; fileName: string }>({
    id: "",
    ossId: "",
    baseFileId: null,
    fileName: "",
  });
  const [interList, setInterList] = useState([]);
  // const [inteverFlag, setInteverFlag] = useState<boolean>(true);
  const [detailArr, setDetailArr] = useState<any>([]);
  const [fileLength, setFileLength] = useState<any>([]);
  const [searchFileParams, setSearchFileParams] = useState<any>({});
  const [currentInterItem, setCurrentInterItem] = useState<any>({});
  const [searchValue, setSearchValue] = useState<string>("");
  let interval = null;
  // 使用 useEffect 钩子设置表单的初始值
  useEffect(() => {
    if (currentData) {
      form.setFieldsValue(currentData);
    } else {
      form.resetFields(); // 如果没有数据，重置表单
    }
  }, [currentData, form]);

  useEffect(() => {
    getUserInfo().then((res) => {
      setUserInfo(res);
    });
    getAllListData(searchParams);
  }, []);

  useEffect(() => {
    if (toDetaile) {
      const processStatus = detailArr.some((item) => item.analyzeProgress < 100);
      if (processStatus) {
        interval = setInterval(() => {
          processData(currentData);
        }, 8000);
      }
      return () => {
        clearInterval(interval);
        interval = null;
      };
    }
  }, [toDetaile, detailArr]);

  const getAllListData = (noteElement) => {
    setLoading(true);
    fetchRequest({
      api: "getKnowledgeData",
      params: noteElement,
      callback: (res) => {
        setLoading(false);
        if (res.code === 200) {
          let data = res.data.records;
          setFileLength(res.data.total);
          setCardData(data);
        } else {
          // 创建报错了给出提示，便签不渲染
          if (res.code !== 401) {
            message.open({
              type: "error",
              content: res.msg,
            });
          }
        }
      },
    });
  };
  const getInterList = (title) => {
    setLoading(true);
    fetchRequest({
      api: "libBaseInfoGetUrl",
      params: {
        title: title,
      },
      callback: async (res) => {
        setLoading(false);
        if (res.code === 200) {
          setInterList(res.data);
        }
      },
    });
  };

  const createKnowledge = () => {
    setAddOredit("add");
    setModal2Open(true);
    setCurrentData({});
    form.resetFields();
  };
  const [loading, setLoading] = useState(false);
  const handlerSubmit = async () => {
    try {
      form.setFieldValue("libType", "TEXT_LIBRARY");
      await form.validateFields();
      const formValues = form.getFieldsValue();
      setLoading(true);
      // 提交逻辑这里
      if (addOredit === "edit") {
        fetchRequest({
          api: currentData.permissionType == "ENTERPRISE" ? "editEnterprise" : "editKnowledge",
          params: {
            ...currentData,
            libDesc: formValues.libDesc,
            libName: formValues.libName,
          },
          callback: (res) => {
            if (res.code === 200) {
              message.open({
                type: "success",
                content: "修改成功",
              });
              getAllListData(searchParams);
              form.resetFields();
              setModal2Open(false); // 关闭模态框
            } else {
              message.open({
                type: "error",
                content: res.msg,
              });
            }
            setLoading(false);
          },
        });
      } else {
        fetchRequest({
          api: "addKnowledge",
          params: {
            libName: formValues.libName,
            libType: "TEXT_LIBRARY",
            libDesc: formValues.libDesc || "",
          },
          callback: (res) => {
            if (res.code === 200) {
              message.open({
                type: "success",
                content: "添加成功",
              });
              setModal2Open(false); // 关闭模态框
              getAllListData(searchParams);
              form.resetFields();
            } else {
              message.open({
                type: "error",
                content: res.msg,
              });
            }
            setLoading(false);
          },
        });
      }
    } catch (error) {
      console.error("Validation Failed:", error);
    }
  };
  // 上传
  const uploadHandler = (item = "") => {
    // setModalUploadOpen(true);
    if (!item) {
      item = currentData;
    }
    setCurrentData(item);
  };
  const handlerUploadSubmit = () => {
    setModalUploadOpen(false);
  };

  const uploadProps: UploadProps = {
    name: "file",
    multiple: true,
    showUploadList: false,
    accept: ".docx,.pptx,.xls,.xlsx,.csv,.txt,.pdf",
    beforeUpload(file) {
      let arr = file.name.split(".");
      let fileName = arr[arr.length - 1] || "";
      let fileFormat = ["docx", "pptx", "xls", "xlsx", "csv", "txt", "pdf"];
      if (!fileFormat.includes(fileName)) {
        message.error("文件格式不正确!");
        return false;
      }

      if (file.size <= 0) {
        message.error("上传文件的大小必须大于0！");
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 15;
      if (!isLt2M) {
        message.error("不允许超过15MB!");
        return false; // 返回 false 则会阻止文件上传
      }
    },
  };

  // 文件转base64
  function fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      // 成功读取文件时的回调
      reader.onload = () => {
        resolve(reader.result); // Base64 编码的字符串
      };

      // 读取文件失败时的回调
      reader.onerror = (error) => {
        reject(error);
      };

      // 读取文件并转为 Base64
      reader.readAsDataURL(file);
    });
  }

  const handleCustomRequest = async (options) => {
    setLoading(true);
    const { file } = options;
    const formData = {
      fileStr: await fileToBase64(file),
      fileName: file.name,
      baseId: currentData.id,
    };
    fetchRequest({
      api: "uploadKnowledgeFile",
      params: formData,
      file: true,
      callback: async (res) => {
        if (res.code === 200) {
          message.success(`操作成功`);
          getAllListData(searchParams);
          if (toDetaile) {
            toDetailHandler(currentData);
          }
        } else {
          message.open({
            type: "error",
            content: "上传失败",
          });
        }
        setLoading(false);
      },
    });
  };

  const editItemHandler = (item) => {
    setCurrentData(item);
    setAddOredit("edit");
    setModal2Open(true);
    form.setFieldsValue(item);
  };
  const deleteItemHandler = (item) => {
    setLoading(true);
    fetchRequest({
      api: item.permissionType === "ENTERPRISE" ? "delEnterpriseKnowledge" : "delKnowledge",
      params: { id: item.id },
      callback: (res) => {
        if (res.code === 200) {
          // setCardData([]);
          // setSearchParams({ ...searchParams, pageNum: 1 });
          getAllListData(searchParams);
          message.open({
            type: "success",
            content: "删除成功",
          });
        } else {
          // 创建报错了给出提示，便签不渲染
          message.open({
            type: "error",
            content: res.msg,
          });
        }
        setLoading(false);
      },
    });
  };

  const fileItems: MenuProps["items"] = [
    {
      key: "1",
      label: "下载",
    },
    {
      key: "2",
      label: "删除",
    },
  ];
  const internItems: MenuProps["items"] = [
    {
      key: "1",
      label: "在新标签页打开",
    },
    {
      key: "3",
      label: (
        <Popconfirm
          trigger="hover"
          placement="bottomLeft"
          getPopupContainer={(triggerNode) => triggerNode.parentNode.parentNode as any}
          title="您确定要删除这条吗?"
          onConfirm={(e) => handleDelete(e, currentInterItem)}
          okText="删除"
          cancelText="取消"
        >
          <label style={{ cursor: "pointer" }}>删除</label>
        </Popconfirm>
      ),
    },
  ];

  const internItemHandler = (obj, item) => {
    if (obj.key == "1") {
      window.open(item.url, "_bank");
    } else {
      setCurrentInterItem(item);
      handleDelete(item);
    }
  };
  const handleDelete = (event, item?) => {
    setLoading(true);
    fetchRequest({
      api: "libBaseInfoDelUrl",
      params: {
        id: item.id,
      },
      callback: async (res) => {
        if (res.code === 200) {
          getInterList("");
        }
      },
    });
  };

  const getType = (fileName) => {
    const typeMap = {
      ".pdf": "application/pdf",
      ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      ".xls": "application/vnd.ms-excel",
      ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ".csv": "text/csv",
      ".txt": "text/plain",
      ".ppt": "application/vnd.ms-powerpoint",
      ".doc": "application/msword",
    };
    const type = typeMap[fileName.split(".").pop().toLowerCase()];
    return type;
  };

  const fileItemHandler: MenuProps["onClick"] = (item) => {
    if (item.key == "1") {
      setLoading(true);
      fetchRequest({
        api: "downLoadFile",
        params: {
          id: currentKnow.ossId,
        },
        callback: (res) => {
          setLoading(false);
          setCurrentKnow({ id: "", ossId: "", baseFileId: null, fileName: "" });
          let type = getType(currentKnow.fileName);
          const blob = base64ToBlob(res, type);
          const aLink = document.createElement("a");
          aLink.style.display = "none";
          aLink.href = URL.createObjectURL(blob);
          aLink.setAttribute("download", currentKnow.fileName); // 设置下载文件名称
          document.body.appendChild(aLink);
          aLink.click();
          URL.revokeObjectURL(aLink.href); // 清除引用
          document.body.removeChild(aLink);
          message.open({
            type: "success",
            content: "下载成功",
          });
        },
      });
    } else {
      setOpen(true);
    }
  };
  const deleKnowFileItem = () => {
    setLoading(true);
    fetchRequest({
      api: "delFile",
      params: { id: currentKnow.id },
      callback: (res) => {
        if (res.code === 200) {
          toDetailHandler(currentData);
          getAllListData(searchParams);
          setItemSelectIndex(null);
          setCurrentKnow({ id: "", ossId: "", baseFileId: null, fileName: "" });
          message.open({
            type: "success",
            content: "删除成功",
          });
        } else {
          // 创建报错了给出提示，便签不渲染
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  const addInterItem = () => {
    const links = document.getElementsByTagName("link");
    let favicon = null;
    for (let i = 0; i < links.length; i++) {
      if (links[i].rel.includes("icon")) {
        favicon = links[i].href;
        break;
      }
    }

    setLoading(true);
    fetchRequest({
      api: "libBaseInfoAddUrl",
      params: {
        title: document.title,
        icon: favicon,
        url: window.location.href,
      },
      callback: async (res) => {
        if (res.code === 200) {
          getInterList("");
        }
      },
    });
  };

  const uploadFileShow = () => {
    setModalFileOpen(true);
  };

  // 文件详情
  const toDetailHandler = (itemPar) => {
    setLoading(true);
    fetchRequest({
      api: "getItemDetail",
      params: {
        pageNum: 1,
        pageSize: 300,
        entity: {
          title: itemPar.searchFileName,
          baseId: itemPar.id,
        },
      },
      callback: (res) => {
        setLoading(false);
        if (res.code === 200) {
          let data = res.data;

          setDetailArr(data.records);
          setItemDetail(data);
          setToDetaile(true);
          setCurrentData(itemPar);

          setknowHome("知识库详情");
        } else {
          // 创建报错了给出提示，便签不渲染
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  const processData = (item) => {
    setLoading(true);
    fetchRequest({
      api: "getItemDetail",
      params: {
        pageNum: 1,
        pageSize: 300,
        entity: {
          title: searchFileParams.searchFileName,
          baseId: item.id,
        },
      },
      callback: (res) => {
        setLoading(false);
        if (res.code === 200) {
          let data = res.data;
          setItemDetail(data);
          const processStatus = data.records.every((item) => item.analyzeProgress == 100);
          if (processStatus) {
            clearInterval(interval);
            interval = null;
          }
        }
      },
    });
  };

  const toInterDetailHandler = () => {
    setInterToDetaile(true);
    setInterFlag(false);
    setSearchValue("");
    setknowHome("网页详情");
    getInterList("");
  };
  const inputChange = (e) => {
    const value = e.target.value.trim();
    // if (!value) return;
    setSearchValue(value);
    // searchParams.entity.libName = value;

    if (knowHome == "首页") {
      const updateValue = { ...searchParams, pageNum: 1, entity: { libName: value, libType: "TEXT_LIBRARY" } };
      setSearchParams(updateValue);
      debounceKnowSearch(updateValue);
    } else if (knowHome == "知识库详情") {
      const updateValue = { ...currentData, id: currentData.id, searchFileName: value };
      setSearchFileParams(updateValue);
      debounceknowDetailSearch(updateValue);
    } else if (knowHome == "网页详情") {
      debounceInterDetailSearch(value);
    }
  };
  const debounceKnowSearch = useCallback(debounce(getAllListData, 500), []);
  const debounceknowDetailSearch = useCallback(debounce(toDetailHandler, 500), []);
  const debounceInterDetailSearch = useCallback(debounce(getInterList, 500), []);

  const handleBack = () => {
    setSearchValue("");
    clearInterval(interval);
    interval = null;
    setCurrentKnow({ id: "", ossId: "", baseFileId: null, fileName: "" });
    setToDetaile(false);
    setInterToDetaile(false);
    setCurrentData({});
    setItemSelectIndex(null);
    setknowHome("首页");
  };
  const handleHistory = () => {
    setOpenHistoryDrawer(true);
  };
  // 关闭历史记录
  const historyDrawerClose = () => {
    setOpenHistoryDrawer(false);
  };
  const historyCallback = (current) => {
    historyDrawerClose();

    fetchRequest({
      api: "getConversation",
      params: {
        appKey: currentAi,
        user: userInfo?.id || "anonymous",
        conversation_id: current.id,
      },
      callback: (res) => {
        const chatList = [];

        // sseChat.setChatList(chatList);
        // sseChat.setDisplayedText(chatList.length > 0 ? chatList[chatList.length - 1][1].content : "");
      },
    });
  };
  const itemHistory = (item, index) => {
    setItemSelectIndex(index);
    setCurrentKnow(item);
  };
  const histotyChat = (item) => {
    setOpenHistoryDrawer(true);
  };

  const placeHolderHandler = () => {
    if (knowHome === "首页") {
      return "搜索知识库";
    } else if (knowHome === "网页详情") {
      return "搜索文档";
    } else {
      return "搜索文件";
    }
  };

  const fileExtensionHandler = (item) => {
    if (item.fileNameSuffix === "pdf") {
      return <span className="extend-icon">{knowdgeSVGIcon.pdf}</span>;
    } else if (item.fileNameSuffix === "docx") {
      return <span className="extend-icon">{knowdgeSVGIcon.word}</span>;
    } else if (item.fileNameSuffix === "xls" || item.fileNameSuffix === "xlsx" || item.fileNameSuffix === "csv") {
      return <span className="extend-icon">{knowdgeSVGIcon.excel}</span>;
    } else if (item.fileNameSuffix === "txt") {
      return <span className="extend-icon">{knowdgeSVGIcon.txt}</span>;
    } else if (item.fileNameSuffix === "pptx") {
      return <span className="extend-icon">{knowdgeSVGIcon.ppt}</span>;
    }
  };

  const [open, setOpen] = useState(false);
  const confirmDelete = () => {
    setOpen(false);
    deleKnowFileItem();
  };

  return (
    <Spin spinning={loading} size="default">
      <Flex className="knowledge-content" vertical>
        <TopTitle
          title={knowHome === "首页" ? "知识库" : knowHome === "网页详情" ? "网页知识库" : currentData.libName}
          titleDetail={knowHome === "首页" ? false : true}
          handleBackPar={handleBack}
        ></TopTitle>
        <Flex className="knowledge-content-input " vertical>
          <Input
            size="large"
            prefix={<SearchOutlined />}
            allowClear
            placeholder={placeHolderHandler()}
            style={{ width: "100%" }}
            onChange={inputChange}
            value={searchValue}
          />
        </Flex>

        {/* 知识库详情 */}
        <Flex className="content-box" vertical>
          {toDetaile && !interFlag && knowHome == "知识库详情" && (
            <>
              <Flex className="knowle-flex item-detail " justify="space-between" align="center">
                <Flex>
                  <LockOutlined className="know-lock-icon " />
                  <Typography.Text className="knowle-person">私人</Typography.Text>
                  <Typography.Text>共 {itemDetail.total} 项</Typography.Text>
                </Flex>
                <Flex>
                  {currentKnow.baseFileId && (
                    <Dropdown
                      placement="bottomRight"
                      overlayClassName="dropdown-delete"
                      autoAdjustOverflow={true}
                      getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                      menu={{
                        items: fileItems,
                        onClick: (key) => fileItemHandler(key),
                        selectable: true,
                      }}
                      arrow={false}
                    >
                      <MoreOutlined className="know-more-icon " />
                    </Dropdown>
                  )}
                </Flex>
              </Flex>
              <Flex style={{ height: "100%" }} vertical>
                {itemDetail.records &&
                  itemDetail.records.map((item, index) => {
                    return (
                      <Flex
                        className={classnames(
                          "card-box-detail knowle-flex",
                          index === itemSelectIndex ? "card-box-detail-active" : " ",
                        )}
                        onClick={() => itemHistory(item, index)}
                        key={index}
                        vertical
                      >
                        <Flex className="top" flex={1} align="center" justify="space-between">
                          <Flex align="center" className="top-left">
                            {fileExtensionHandler(item)}
                            <Row className="left-gas">
                              <Col span={24} className="first-title">
                                {item.title}
                              </Col>
                              <Col span={12} className="progress-con">
                                {item.analyzeProgress < 100 ? (
                                  <Flex align="center">
                                    <Progress
                                      percent={item.analyzeProgress}
                                      size={14}
                                      type="circle"
                                      format={() => null}
                                    />
                                    <span className="progress-num">
                                      上传中<span>{item.analyzeProgress}%</span>
                                    </span>
                                  </Flex>
                                ) : (
                                  <p className="two-title">{item.fileSize + "MB"}</p>
                                )}
                              </Col>
                              <Col className="know-detail-time" span={12}>
                                {formatDate(item.uploadTime)}
                              </Col>
                            </Row>
                          </Flex>
                          {/* 问答历史 */}
                          {/* {index === itemSelectIndex && (
                            <Flex justify="end">
                              <Typography.Text onClick={() => histotyChat(item)} className="knowle-detail-icon">
                                <HistoryOutlined className="know-more-icon " />
                              </Typography.Text>
                            </Flex>
                          )} */}
                        </Flex>
                      </Flex>
                    );
                  })}
              </Flex>
              {/* 问答历史 */}
              {/* <HistoryDrawer
                currentAi={currentAi}
                userInfo={userInfo}
                visible={openHistoryDrawer}
                handleClose={historyDrawerClose}
                handleBack={historyCallback}
              ></HistoryDrawer> */}
              <Flex>
                <Upload {...uploadProps} maxCount={5} customRequest={handleCustomRequest}>
                  <Flex className="btn">
                    <Button block size="large" className="submit-btn" icon={<UploadOutlined />}>
                      上传文档
                    </Button>
                  </Flex>
                </Upload>
              </Flex>
            </>
          )}
          {/* 网页知识库详情 */}
          {interToDetaile && knowHome == "网页详情" && (
            <>
              <Flex style={{ height: "100%" }} vertical>
                {interList.length > 0 &&
                  interList.map((item, index) => {
                    return (
                      <Flex className="card-box-detail" vertical key={index}>
                        <Flex className="top" align="center">
                          <Image preview={false} src={item.icon} width={24} height={24}></Image>
                          <Row className="left-gas">
                            <Col span={24} className="first-title">
                              {item.title}
                            </Col>
                            <Col span={24} className="two-title">
                              {item.url}
                            </Col>
                          </Row>
                          <Flex justify="end" className="right-gas">
                            <Dropdown
                              placement="bottomRight"
                              autoAdjustOverflow={true}
                              getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                              menu={{
                                items: internItems,
                                onClick: (key) => internItemHandler(key, item),
                                selectable: true,
                              }}
                              arrow={false}
                            >
                              <MoreOutlined className="know-more-icon " />
                            </Dropdown>
                          </Flex>
                        </Flex>
                      </Flex>
                    );
                  })}
              </Flex>

              <Flex className="btn">
                <Button block size="large" icon={<FileAddOutlined />} onClick={addInterItem} className="submit-btn">
                  添加当前网页
                </Button>
              </Flex>
            </>
          )}
          {/* 网页知识库 */}
          <Flex style={{ height: "100%" }} vertical>
            {!interToDetaile && !toDetaile && knowHome == "首页" && (
              <>
                <Flex className="card-box cardInter" vertical>
                  <Flex className="top" onClick={toInterDetailHandler} align="center">
                    <DesktopOutlined className="know-inter-icon" />
                    <Row className="left-gas">
                      <Col span={24} className="first-title">
                        网页知识库
                      </Col>
                      <Col span={24} className="two-title">
                        知识库描述
                      </Col>
                    </Row>
                  </Flex>
                  <Flex className="tools" justify="space-between">
                    <Flex className="left">
                      <FileOutlined className="know-base-icon" />
                      <Typography.Text className="left-name">{interList.length}</Typography.Text>
                    </Flex>
                  </Flex>
                </Flex>

                {cardData &&
                  cardData.map((item, index) => {
                    return (
                      <Flex className="card-box" key={index} vertical>
                        <Flex className="top" onClick={() => toDetailHandler(item)} align="center">
                          <DatabaseOutlined className="know-file-icon" />
                          <Flex className="left-gas card-box-content" vertical flex={1}>
                            <Typography.Text className="first-title">{item.libName}</Typography.Text>
                            <Typography.Text className="two-title">{item.libDesc}</Typography.Text>
                          </Flex>
                        </Flex>
                        <Flex className="tools" justify="space-between">
                          <Flex className="left konw-left" align="center">
                            <FileOutlined className="know-base-icon" />
                            <Typography.Text className="left-name">{item.documentCount}</Typography.Text>

                            <FolderOutlined className="know-base-icon sino-relation-icon" />
                            <Typography.Text className="left-name">{item.allFileSize + "MB"}</Typography.Text>
                            {/* <span onClick={uploadFileShow}>上传中</span> */}
                          </Flex>
                          <Flex className="right knowle-flex">
                            <Flex onClick={() => editItemHandler(item)}>
                              <Tooltip
                                placement="top"
                                title="编辑"
                                getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                              >
                                <Button
                                  icon={<EditOutlined className="know-edit-icon " />}
                                  type="text"
                                  size="small"
                                ></Button>
                              </Tooltip>
                            </Flex>
                            <Flex onClick={() => uploadHandler(item)}>
                              <Tooltip
                                placement="top"
                                title="上传"
                                getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                              >
                                <Upload {...uploadProps} maxCount={5} customRequest={handleCustomRequest}>
                                  <Button
                                    icon={<UploadOutlined className="know-edit-icon know-upload-icon" />}
                                    type="text"
                                    size="small"
                                  ></Button>
                                </Upload>
                              </Tooltip>
                            </Flex>

                            <Flex className="tools-right">
                              <Popconfirm
                                title="确认删除？"
                                onConfirm={() => deleteItemHandler(item)}
                                getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                                okText="确认"
                                cancelText="取消"
                              >
                                <Tooltip
                                  placement="top"
                                  title="删除"
                                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                                >
                                  <Button
                                    icon={<DeleteOutlined className="know-edit-icon" />}
                                    type="text"
                                    size="small"
                                  ></Button>
                                </Tooltip>
                              </Popconfirm>
                            </Flex>
                          </Flex>
                        </Flex>
                      </Flex>
                    );
                  })}
              </>
            )}
          </Flex>
        </Flex>
        {!toDetaile && !interToDetaile && (
          <Flex className="btn">
            <Button block size="large" icon={<PlusOutlined />} className="submit-btn" onClick={createKnowledge}>
              新建知识库
            </Button>
          </Flex>
        )}

        <Modal
          title={
            <>
              <ExclamationCircleOutlined style={{ color: "var(--ant-color-warning)" }} /> 提示
            </>
          }
          width={250}
          open={open}
          onOk={confirmDelete}
          getContainer={getContainer}
          onCancel={() => setOpen(false)}
          okText="确认"
          cancelText="取消"
        >
          <p>{`确认要删除${currentKnow.fileName}吗？`}</p>
        </Modal>

        <Modal
          title="上传文件"
          centered
          width={"84%"}
          getContainer={getContainer}
          open={modalUploadOpen}
          onOk={handlerUploadSubmit}
          onCancel={() => setModalUploadOpen(false)}
          okText="确认"
          cancelText="取消"
          footer={null}
        >
          <Upload {...uploadProps} maxCount={5} customRequest={handleCustomRequest}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-hint uploadTitle">
              点击活拖拽文件到此上传，每次最多上传 5 个，支持扩展名：pdf 、 docx 、 excel 、 pptx
            </p>
          </Upload>
        </Modal>

        <Modal
          title={addOredit === "add" ? "新建知识库" : "编辑知识库"}
          centered
          width={"84%"}
          getContainer={getContainer}
          open={modal2Open}
          onCancel={() => setModal2Open(false)}
          footer={null}
          okText="确认"
          cancelText="取消"
        >
          <Form form={form} name="validateOnly" layout="vertical" autoComplete="off" onFinish={handlerSubmit}>
            <Form.Item label="知识库类型" rules={[{ required: false, message: "必填项" }]}>
              <Select
                defaultValue="TEXT_LIBRARY"
                options={[
                  {
                    value: "TEXT_LIBRARY",
                    label: "文本库",
                  },
                ]}
              />
            </Form.Item>
            <Form.Item name="libName" label="知识库名称" rules={[{ required: true, message: "必填项" }]}>
              <Input showCount maxLength={50} />
            </Form.Item>
            <Form.Item>
              <Form.Item label="描述" rules={[{ required: false }]} name="libDesc">
                <TextArea rows={3} showCount maxLength={200} />
              </Form.Item>
            </Form.Item>
            <Flex justify="flex-end" gap="small">
              <Button htmlType="button" onClick={() => setModal2Open(false)} className="setup-model-btn-cancel">
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存
              </Button>
            </Flex>
          </Form>
        </Modal>
      </Flex>
    </Spin>
  );
};

export default Knowledge;
